{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "broken_count": 1, "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1*********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "broken_count": 2, "broken_until": "**********", "host": "targon.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "broken_until": "1*********", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", true, 0], "broken_count": 2, "broken_until": "**********", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 24, "broken_until": "**********", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 3, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://clients2.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://static.cloudflareinsights.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://cdn.amplitude.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://sr-client-cfg.amplitude.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://targon.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://api2.amplitude.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401192310381621", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3VwZGF0ZS5nb29nbGVhcGlzLmNvbQAAAA==", false, 0], "server": "https://update.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398686716971190", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", true, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401192320432472", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401192335024168", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rhcmdvbi5jb20AAA==", false, 0], "server": "https://api-sr.amplitude.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "3G"}}}