const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const TargonRegister = require('../targon-register');

class TargonWebServer {
    constructor() {
        this.app = express();
        this.port = 3000;
        this.isRegistering = false;
        this.shouldStop = false;

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(__dirname));
    }

    setupRoutes() {
        // 主页
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'web-interface.html'));
        });

        // 开始注册
        this.app.post('/api/register', async (req, res) => {
            if (this.isRegistering) {
                return res.status(400).json({ error: '注册任务正在进行中' });
            }

            const { count, concurrency, mode } = req.body;
            
            if (!count || count < 1 || count > 1000) {
                return res.status(400).json({ error: '注册数量必须在1-1000之间' });
            }

            if (!concurrency || concurrency < 1 || concurrency > 10) {
                return res.status(400).json({ error: '并发数必须在1-10之间' });
            }

            this.isRegistering = true;
            this.shouldStop = false;

            // 设置流式响应
            res.writeHead(200, {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            });

            try {
                if (mode === 'parallel') {
                    await this.runParallelRegistration(count, concurrency, res);
                } else {
                    await this.runSerialRegistration(count, res);
                }
            } catch (error) {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `❌ 注册过程出错: ${error.message}`
                });
            } finally {
                this.isRegistering = false;
                res.end();
            }
        });

        // 停止注册
        this.app.post('/api/stop', (req, res) => {
            this.shouldStop = true;
            res.json({ message: '已请求停止注册' });
        });

        // 获取账户列表
        this.app.get('/api/accounts', async (req, res) => {
            try {
                const accounts = await this.loadAccounts();
                res.json(accounts);
            } catch (error) {
                res.status(500).json({ error: '加载账户列表失败' });
            }
        });


    }

    async runParallelRegistration(count, concurrency, res) {
        this.sendUpdate(res, {
            type: 'log',
            message: `🚀 开始并行注册 ${count} 个账户，并发数: ${concurrency}`
        });

        const results = [];
        const promises = [];
        let completed = 0;

        // 创建注册任务
        for (let i = 1; i <= count; i++) {
            if (this.shouldStop) break;

            // 为每个任务创建独立的注册实例，避免状态混乱
            const promise = this.registerSingleAccountWithIndependentInstance(i, count, res);
            promises.push(promise);

            // 控制并发数
            if (promises.length >= concurrency || i === count) {
                const batchResults = await Promise.allSettled(promises);

                // 处理结果
                for (const result of batchResults) {
                    completed++;

                    if (result.status === 'fulfilled' && result.value) {
                        results.push(result.value);
                        this.sendUpdate(res, {
                            type: 'account',
                            account: result.value
                        });
                    } else {
                        results.push(null);
                    }

                    this.sendUpdate(res, {
                        type: 'progress',
                        current: completed,
                        total: count
                    });
                }

                // 清空当前批次
                promises.length = 0;

                // 如果不是最后一批，稍微等待一下
                if (i < count && !this.shouldStop) {
                    await this.sleep(1000);
                }
            }
        }

        const successCount = results.filter(r => r !== null).length;
        this.sendUpdate(res, {
            type: 'complete',
            success: successCount,
            failed: count - successCount,
            total: count
        });
    }

    async runSerialRegistration(count, res) {
        this.sendUpdate(res, {
            type: 'log',
            message: `🚀 开始串行注册 ${count} 个账户，并发数: 1`
        });

        const results = [];

        for (let i = 1; i <= count; i++) {
            if (this.shouldStop) break;

            // 为每个任务创建独立的注册实例
            const result = await this.registerSingleAccountWithIndependentInstance(i, count, res);
            results.push(result);

            if (result) {
                this.sendUpdate(res, {
                    type: 'account',
                    account: result
                });
            }

            this.sendUpdate(res, {
                type: 'progress',
                current: i,
                total: count
            });

            // 如果不是最后一个账户，等待一下
            if (i < count && !this.shouldStop) {
                await this.sleep(2000);
            }
        }

        const successCount = results.filter(r => r !== null).length;
        this.sendUpdate(res, {
            type: 'complete',
            success: successCount,
            failed: count - successCount,
            total: count
        });
    }

    // 使用独立实例的注册方法，避免状态混乱
    async registerSingleAccountWithIndependentInstance(index, total, res) {
        try {
            // 为每个任务创建独立的TargonRegister实例
            const register = new TargonRegister();

            // 生成独立的邮箱和密码
            const email = register.generateRandomEmail();
            const password = register.generateRandomPassword();

            // 执行完整的注册流程
            const result = await this.executeRegistrationFlow(register, email, password, index, total, res);

            if (result) {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `🎉 注册成功! API: ${result.apiKey?.slice(-8) || 'N/A'} | 2FA: ${result.twoFactorEnabled ? '✅' : '❌'}`
                });
            } else {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `❌ 注册失败`
                });
            }

            return result;
        } catch (error) {
            this.sendUpdate(res, {
                type: 'log',
                message: `❌ 注册失败: ${error.message}`
            });
            return null;
        }
    }

    // 执行完整的注册流程
    async executeRegistrationFlow(register, email, password, index, total, res) {
        try {
            // 1. 检查邮箱
            await register.checkEmail(email);

            // 2. 创建账户
            await register.createAccount(email, password);

            // 3. 等待验证邮件
            const verificationLink = await register.waitForVerificationEmail(email);

            // 4. 验证邮箱
            const verified = await register.verifyEmail(verificationLink);

            if (!verified) {
                throw new Error('邮箱验证失败');
            }

            // 等待几秒让系统处理验证状态
            await this.sleep(3000);

            // 5. 登录账户
            const loginResult = await register.signIn(email, password);

            if (!loginResult) {
                throw new Error('登录失败');
            }

            // 等待登录状态生效
            await this.sleep(2000);

            // 6. 获取用户信息和API Key
            const userInfoResult = await register.getCompleteUserInfo();
            let apiKey = null;

            if (userInfoResult && userInfoResult.apiKey) {
                apiKey = userInfoResult.apiKey;
            } else {
                // 重试一次
                await this.sleep(2000);
                const retryResult = await register.getCompleteUserInfo();
                if (retryResult && retryResult.apiKey) {
                    apiKey = retryResult.apiKey;
                }
            }

            // 7. 设置2FA并获取0.2美元奖励
            const twoFAResult = await register.setup2FA();
            let twoFactorData = null;
            let twoFactorEnabled = false;

            if (twoFAResult && twoFAResult.success) {
                twoFactorEnabled = true;
                twoFactorData = {
                    manualCode: twoFAResult.manualCode,
                    twoFactorSecret: twoFAResult.twoFactorSecret,
                    uri: twoFAResult.uri
                };
            } else {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `[${index}/${total}] ⚠️ 2FA设置失败: ${twoFAResult?.error || '未知错误'}`
                });
            }

            // 8. 获取最新的用户信息和余额
            await this.sleep(2000); // 等待余额更新
            const finalUserInfo = await register.getCompleteUserInfo();

            const accountInfo = {
                email: email,
                password: password,
                verificationLink: verificationLink,
                apiKey: apiKey,
                status: 'verified',
                loginStatus: true,
                twoFactorEnabled: twoFactorEnabled,
                twoFactorData: twoFactorData,
                creditBalance: finalUserInfo ? {
                    boughtCredits: finalUserInfo.boughtCredits,
                    planCredits: finalUserInfo.planCredits,
                    totalCredits: (finalUserInfo.boughtCredits || 0) + (finalUserInfo.planCredits || 0)
                } : null,
                registeredAt: new Date().toISOString()
            };

            // 保存账户信息
            await register.saveAccountToFile(accountInfo);

            return accountInfo;

        } catch (error) {
            // 不保存失败的账户信息，只抛出错误
            throw error;
        }
    }

    sendUpdate(res, data) {
        try {
            res.write(JSON.stringify(data) + '\n');
        } catch (error) {
            console.error('发送更新失败:', error);
        }
    }

    async loadAccounts() {
        try {
            // 创建临时实例获取文件路径
            const tempRegister = new TargonRegister();
            const fileContent = await fs.readFile(tempRegister.outputFile, 'utf8');
            return JSON.parse(fileContent);
        } catch (error) {
            return [];
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`🌐 Targon Web Interface 已启动`);
            console.log(`📱 访问地址: http://localhost:${this.port}`);
            console.log(`🚀 准备开始批量注册 Targon 账户！`);
        });
    }
}

// 启动服务器
if (require.main === module) {
    const server = new TargonWebServer();
    server.start();
}

module.exports = TargonWebServer;
