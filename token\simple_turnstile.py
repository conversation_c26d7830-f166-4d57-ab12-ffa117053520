#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Turnstile Token 获取服务
去除 emoji 字符，避免编码问题
"""

from DrissionPage import Chromium, ChromiumOptions
import os
import time
import json
import shutil
import sys

def get_turnstile_token():
    """获取 Turnstile Token"""
    
    # 设置数据保存目录
    DATA_DIR = os.path.join(os.getcwd(), 'browser_data_temp')
    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)
    
    browser = None
    
    try:
        print("[INFO] 开始获取 Turnstile Token...")
        
        # 初始化浏览器
        co = ChromiumOptions().set_user_data_path(DATA_DIR)
        browser = Chromium(co)
        page = browser.latest_tab
        
        print("[INFO] 访问注册页面...")
        page.get('https://targon.com/sign-in?mode=signup')
        
        # 等待页面加载
        print("[INFO] 等待页面加载...")
        time.sleep(5)
        
        # 智能等待 Token 生成
        print("[INFO] 等待 Turnstile Token 生成...")
        max_attempts = 30  # 最多等待 60 秒
        attempt = 0
        
        while attempt < max_attempts:
            try:
                # 查找 Turnstile 输入框
                turnstile_input = page.ele('css:input[name="cf-turnstile-response"]')
                
                if turnstile_input:
                    token_value = turnstile_input.attr('value')
                    token_length = len(token_value) if token_value else 0
                    
                    print(f"[CHECK] 第 {attempt + 1} 次检查 - Token 长度: {token_length}")
                    
                    # 检查 Token 是否有效（长度大于 100）
                    if token_value and token_length > 100:
                        # 返回结果 - 只输出 JSON，不输出其他日志
                        result = {
                            'success': True,
                            'token': token_value,
                            'length': token_length,
                            'timestamp': time.time(),
                            'message': 'Success'
                        }

                        print(json.dumps(result, ensure_ascii=True))
                        return result
                    
                else:
                    print(f"[WARN] 第 {attempt + 1} 次检查 - 未找到 Turnstile 输入框")
                
            except Exception as e:
                print(f"[WARN] 第 {attempt + 1} 次检查出错: {str(e)}")
            
            attempt += 1
            time.sleep(2)  # 等待 2 秒后重试
        
        print("[ERROR] 超时未能获取到有效的 Turnstile Token")
        
        # 返回失败结果
        result = {
            'success': False,
            'token': None,
            'length': 0,
            'timestamp': time.time(),
            'message': '超时未能获取到有效的 Turnstile Token'
        }
        
        print(json.dumps(result, ensure_ascii=False))
        return result
        
    except Exception as e:
        print(f"[ERROR] 程序执行出错: {str(e)}")
        
        # 返回错误结果
        result = {
            'success': False,
            'token': None,
            'length': 0,
            'timestamp': time.time(),
            'message': f'程序执行出错: {str(e)}'
        }
        
        print(json.dumps(result, ensure_ascii=False))
        return result
        
    finally:
        # 清理资源
        try:
            if browser:
                print("[INFO] 关闭浏览器...")
                browser.quit()
            
            # 删除临时数据目录
            if os.path.exists(DATA_DIR):
                print("[INFO] 删除浏览器数据目录...")
                shutil.rmtree(DATA_DIR)
                print("[INFO] 浏览器数据目录已删除")
                
        except Exception as cleanup_error:
            print(f"[WARN] 清理资源失败: {str(cleanup_error)}")

if __name__ == "__main__":
    get_turnstile_token()
