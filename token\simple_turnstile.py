#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多标签页 Turnstile Token 池管理服务
支持并发获取和管理多个 Token
"""

from DrissionPage import Chromium, ChromiumOptions
import os
import time
import json
import shutil
import sys
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed

class TurnstileTokenPool:
    """Turnstile Token 池管理类"""

    def __init__(self, max_tabs=10):
        self.max_tabs = max_tabs
        self.browser = None
        self.tabs = []
        self.token_queue = queue.Queue()
        self.running = False
        self.data_dir = os.path.join(os.getcwd(), 'browser_data_pool')

        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

    def start_browser(self):
        """启动浏览器并创建标签页"""
        try:
            print(f"[INFO] 启动浏览器，准备创建 {self.max_tabs} 个标签页...")

            # 初始化浏览器
            co = ChromiumOptions().set_user_data_path(self.data_dir)
            co.headless(False)  # 可见模式，便于调试
            self.browser = Chromium(co)

            # 创建多个标签页
            for i in range(self.max_tabs):
                if i == 0:
                    # 使用默认标签页
                    tab = self.browser.latest_tab
                else:
                    # 创建新标签页
                    tab = self.browser.new_tab()

                self.tabs.append({
                    'id': i,
                    'tab': tab,
                    'token': None,
                    'status': 'ready',  # ready, loading, error
                    'last_update': 0
                })
                print(f"[INFO] 标签页 {i} 创建成功")

            print(f"[INFO] 浏览器启动完成，共创建 {len(self.tabs)} 个标签页")
            return True

        except Exception as e:
            print(f"[ERROR] 启动浏览器失败: {str(e)}")
            return False

    def get_token_from_tab(self, tab_info):
        print("=== GET_TOKEN_FROM_TAB 函数被调用了！===")
        
        """从单个标签页获取 Token"""
        tab_id = tab_info['id']
        tab = tab_info['tab']

        try:
            print(f"[INFO] 标签页 {tab_id} 开始获取 Token...")
            tab_info['status'] = 'loading'

            # 访问注册页面
            tab.get('https://targon.com/sign-in?mode=signup')
            print('打开页面')

            # 等待页面加载
            time.sleep(3)

            # 等待 Token 生成
            max_attempts = 20
            for attempt in range(max_attempts):
                try:
                    turnstile_input = tab.ele('@name=cf-turnstile-response')

                    if turnstile_input:
                        token_value = turnstile_input.attr('value')
                        token_length = len(token_value) if token_value else 0

                        if token_value and token_length > 100:
                            tab_info['token'] = token_value
                            tab_info['status'] = 'ready'
                            tab_info['last_update'] = time.time()

                            print(f"[SUCCESS] 标签页 {tab_id} 获取到 Token (长度: {token_length})")
                            return token_value

                    time.sleep(1)

                except Exception as e:
                    print(f"[WARN] 标签页 {tab_id} 第 {attempt + 1} 次检查出错: {str(e)}")

            print(f"[ERROR] 标签页 {tab_id} 获取 Token 超时")
            tab_info['status'] = 'error'
            return None

        except Exception as e:
            print(f"[ERROR] 标签页 {tab_id} 获取 Token 失败: {str(e)}")
            tab_info['status'] = 'error'
            return None

    def refresh_all_tabs(self):
        """并发刷新所有标签页获取新 Token"""
        print("[INFO] 开始并发刷新所有标签页...")

        with ThreadPoolExecutor(max_workers=self.max_tabs) as executor:
            # 提交所有标签页的刷新任务
            future_to_tab = {
                executor.submit(self.get_token_from_tab, tab_info): tab_info
                for tab_info in self.tabs
            }

            # 收集结果
            tokens = []
            for future in as_completed(future_to_tab):
                tab_info = future_to_tab[future]
                try:
                    token = future.result()
                    if token:
                        tokens.append({
                            'tab_id': tab_info['id'],
                            'token': token,
                            'timestamp': time.time()
                        })
                        # 将 Token 放入队列
                        self.token_queue.put(token)
                except Exception as e:
                    print(f"[ERROR] 标签页 {tab_info['id']} 刷新失败: {str(e)}")

            print(f"[INFO] 并发刷新完成，获取到 {len(tokens)} 个有效 Token")
            return tokens

    def get_available_token(self):
        """获取一个可用的 Token"""
        try:
            # 从队列中获取 Token（非阻塞）
            token = self.token_queue.get_nowait()
            print(f"[INFO] 分配 Token: {token[:50]}...")
            return token
        except queue.Empty:
            print("[WARN] Token 池为空，尝试刷新...")
            # 如果队列为空，尝试刷新一个标签页
            for tab_info in self.tabs:
                if tab_info['status'] == 'ready':
                    token = self.get_token_from_tab(tab_info)
                    if token:
                        return token
            return None

    def start_pool(self):
        """启动 Token 池"""
        print("[INFO] 启动 Turnstile Token 池...")

        if not self.start_browser():
            return False

        # 初始化所有标签页的 Token
        tokens = self.refresh_all_tabs()

        if tokens:
            print(f"[SUCCESS] Token 池启动成功，初始 Token 数量: {len(tokens)}")
            self.running = True
            return True
        else:
            print("[ERROR] Token 池启动失败，未获取到任何 Token")
            return False

    def stop_pool(self):
        """停止 Token 池"""
        print("[INFO] 停止 Token 池...")
        self.running = False

        if self.browser:
            try:
                self.browser.quit()
                print("[INFO] 浏览器已关闭")
            except Exception as e:
                print(f"[WARN] 关闭浏览器失败: {str(e)}")

        # 清理数据目录
        try:
            if os.path.exists(self.data_dir):
                shutil.rmtree(self.data_dir)
                print("[INFO] 浏览器数据目录已删除")
        except Exception as e:
            print(f"[WARN] 清理数据目录失败: {str(e)}")

    def get_pool_status(self):
        """获取 Token 池状态"""
        status = {
            'running': self.running,
            'total_tabs': len(self.tabs),
            'available_tokens': self.token_queue.qsize(),
            'tabs_status': []
        }

        for tab_info in self.tabs:
            status['tabs_status'].append({
                'id': tab_info['id'],
                'status': tab_info['status'],
                'has_token': tab_info['token'] is not None,
                'last_update': tab_info['last_update']
            })

        return status


def main():
    """主函数 - 支持命令行参数"""
    if len(sys.argv) < 2:
        print("用法: python simple_turnstile.py <command> [args]")
        print("命令:")
        print("  start <max_tabs>  - 启动 Token 池")
        print("  get               - 获取一个 Token")
        print("  status            - 查看池状态")
        print("  stop              - 停止池")
        return

    command = sys.argv[1].lower()

    if command == 'start':
        max_tabs = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        pool = TurnstileTokenPool(max_tabs)

        if pool.start_pool():
            # 输出启动成功的 JSON 结果
            result = {
                'success': True,
                'message': 'Token pool started successfully',
                'max_tabs': max_tabs,
                'available_tokens': pool.token_queue.qsize()
            }
            print(json.dumps(result))

            # 保持运行状态，等待外部调用
            try:
                while pool.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n[INFO] 收到中断信号，正在停止...")
                pool.stop_pool()
        else:
            result = {
                'success': False,
                'message': 'Failed to start token pool'
            }
            print(json.dumps(result))

    elif command == 'get':
        # 这里需要与运行中的池通信，暂时返回错误
        result = {
            'success': False,
            'message': 'Pool communication not implemented yet'
        }
        print(json.dumps(result))

    elif command == 'status':
        # 这里需要与运行中的池通信，暂时返回错误
        result = {
            'success': False,
            'message': 'Pool communication not implemented yet'
        }
        print(json.dumps(result))

    elif command == 'stop':
        # 这里需要与运行中的池通信，暂时返回错误
        result = {
            'success': False,
            'message': 'Pool communication not implemented yet'
        }
        print(json.dumps(result))

    else:
        result = {
            'success': False,
            'message': f'Unknown command: {command}'
        }
        print(json.dumps(result))


if __name__ == "__main__":
    main()
