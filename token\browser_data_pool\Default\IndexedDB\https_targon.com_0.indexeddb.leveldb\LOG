2025/08/02-17:29:04.889 34d4 Reusing MANIFEST C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/MANIFEST-000001
2025/08/02-17:29:04.890 34d4 Recovering log #20
2025/08/02-17:29:04.891 34d4 Reusing old log C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/000020.log 
2025/08/02-17:29:04.906 1de4 Level-0 table #25: started
2025/08/02-17:29:04.915 1de4 Level-0 table #25: 321715 bytes OK
2025/08/02-17:29:04.920 1de4 Delete type=0 #20
2025/08/02-17:29:04.921 34d4 Compacting 4@0 + 0@1 files
2025/08/02-17:29:04.928 34d4 Generated table #26@0: 285 keys, 6762 bytes
2025/08/02-17:29:04.928 34d4 Compacted 4@0 + 0@1 files => 6762 bytes
2025/08/02-17:29:04.933 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:29:04.934 34d4 Delete type=2 #25
2025/08/02-17:29:04.934 1de4 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:29:04.986 1de4 Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:29:05.132 1de4 Level-0 table #28: started
2025/08/02-17:29:05.140 1de4 Level-0 table #28: 164689 bytes OK
2025/08/02-17:29:05.145 1de4 Delete type=2 #17
2025/08/02-17:29:05.145 1de4 Delete type=2 #19
2025/08/02-17:29:05.145 1de4 Delete type=2 #21
2025/08/02-17:29:05.145 1de4 Delete type=0 #24
2025/08/02-17:29:05.146 1de4 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x07\x00\x00\x05' @ 2169 : 1
2025/08/02-17:29:05.146 1de4 Compacting 1@0 + 1@1 files
2025/08/02-17:29:05.161 1de4 Generated table #29@0: 301 keys, 40133 bytes
2025/08/02-17:29:05.161 1de4 Compacted 1@0 + 1@1 files => 40133 bytes
2025/08/02-17:29:05.164 1de4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:29:05.164 34d4 Manual compaction at level-0 from '\x00\x07\x00\x00\x05' @ 2169 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:29:05.193 1de4 Level-0 table #31: started
2025/08/02-17:29:05.201 1de4 Level-0 table #31: 516 bytes OK
2025/08/02-17:29:05.204 1de4 Delete type=2 #26
2025/08/02-17:29:05.204 1de4 Delete type=0 #27
2025/08/02-17:29:05.204 1de4 Delete type=2 #28
2025/08/02-17:29:05.205 34d4 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x08\x00\x00\x05' @ 2185 : 1
2025/08/02-17:29:05.205 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:29:05.211 34d4 Generated table #32@0: 302 keys, 40161 bytes
2025/08/02-17:29:05.211 34d4 Compacted 1@0 + 1@1 files => 40161 bytes
2025/08/02-17:29:05.214 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:29:05.214 34d4 Delete type=2 #31
2025/08/02-17:29:05.214 34d4 Manual compaction at level-0 from '\x00\x08\x00\x00\x05' @ 2185 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
