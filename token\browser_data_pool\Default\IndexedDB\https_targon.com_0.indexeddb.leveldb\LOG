2025/08/02-17:19:48.836 5c34 Creating DB C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb since it was missing.
2025/08/02-17:19:48.853 5c34 Reusing MANIFEST C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/MANIFEST-000001
2025/08/02-17:19:48.888 2fc4 Level-0 table #5: started
2025/08/02-17:19:48.896 2fc4 Level-0 table #5: 2576 bytes OK
2025/08/02-17:19:48.903 2fc4 Delete type=0 #3
2025/08/02-17:19:48.904 5c34 Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:19:48.904 5c34 Level-0 table #7: started
2025/08/02-17:19:48.913 5c34 Level-0 table #7: 477 bytes OK
2025/08/02-17:19:48.919 5c34 Delete type=0 #4
2025/08/02-17:19:48.920 5c34 Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:19:48.920 5c34 Manual compaction at level-1 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:19:49.006 2fc4 Level-0 table #9: started
2025/08/02-17:19:49.013 2fc4 Level-0 table #9: 500 bytes OK
2025/08/02-17:19:49.021 2fc4 Delete type=0 #6
2025/08/02-17:19:49.021 5c34 Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:19:49.021 5c34 Manual compaction at level-1 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:19:49.022 5c34 Level-0 table #11: started
2025/08/02-17:19:49.029 5c34 Level-0 table #11: 33037 bytes OK
2025/08/02-17:19:49.037 5c34 Delete type=0 #8
2025/08/02-17:19:49.037 5c34 Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:19:49.037 5c34 Manual compaction at level-1 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
