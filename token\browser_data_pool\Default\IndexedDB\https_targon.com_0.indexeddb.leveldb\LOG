2025/08/02-17:51:54.439 3f90 Reusing MANIFEST C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/MANIFEST-000001
2025/08/02-17:51:54.440 3f90 Recovering log #76
2025/08/02-17:51:54.444 3f90 Reusing old log C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/000076.log 
2025/08/02-17:51:54.458 3334 Level-0 table #82: started
2025/08/02-17:51:54.522 3334 Level-0 table #82: 3269159 bytes OK
2025/08/02-17:51:54.528 3334 Delete type=0 #76
2025/08/02-17:51:54.529 3f90 Manual compaction at level-0 from '\x00\x13\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x14\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:51:54.530 3334 Level-0 table #84: started
2025/08/02-17:51:54.537 3334 Level-0 table #84: 98670 bytes OK
2025/08/02-17:51:54.541 3334 Delete type=0 #81
2025/08/02-17:51:54.542 3f90 Manual compaction at level-0 from '\x00\x14\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x15\x00\x00\x00' @ 0 : 0; will stop at '\x00\x14\x00\x00\x05' @ 6026 : 1
2025/08/02-17:51:54.542 3f90 Compacting 1@0 + 1@1 files
2025/08/02-17:51:54.551 3f90 Generated table #85@0: 828 keys, 107791 bytes
2025/08/02-17:51:54.551 3f90 Compacted 1@0 + 1@1 files => 107791 bytes
2025/08/02-17:51:54.555 3f90 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:51:54.556 3f90 Delete type=2 #84
2025/08/02-17:51:54.556 3f90 Manual compaction at level-0 from '\x00\x14\x00\x00\x05' @ 6026 : 1 .. '\x00\x15\x00\x00\x00' @ 0 : 0; will stop at (end)
