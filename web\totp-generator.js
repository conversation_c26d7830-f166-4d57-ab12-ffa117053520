const { authenticator } = require('otplib');

/**
 * 生成TOTP验证码
 * @param {string} secret - Base32编码的密钥
 * @returns {string} 6位验证码
 */
function generateTOTP(secret) {
    try {
        return authenticator.generate(secret);
    } catch (error) {
        console.error('生成TOTP验证码失败:', error.message);
        return null;
    }
}

/**
 * 验证TOTP验证码
 * @param {string} token - 6位验证码
 * @param {string} secret - Base32编码的密钥
 * @returns {boolean} 验证是否成功
 */
function verifyTOTP(token, secret) {
    try {
        return authenticator.verify({ token, secret });
    } catch (error) {
        console.error('验证TOTP验证码失败:', error.message);
        return false;
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    // 测试用的密钥
    const secretKey = 'WVA5DBCHKRNA4Z72V4YKQBAWUTF7ADPN';
    
    console.log('=== TOTP验证码生成器测试 ===');
    console.log('密钥:', secretKey);
    
    const token = generateTOTP(secretKey);
    if (token) {
        console.log('当前验证码:', token);
        
        // 验证生成的验证码
        const isValid = verifyTOTP(token, secretKey);
        console.log('验证码是否有效:', isValid);
    }
}

module.exports = {
    generateTOTP,
    verifyTOTP
};
