class TargonWebInterface {
    constructor() {
        this.isRegistering = false;
        this.accounts = [];
        this.currentProgress = 0;
        this.totalTasks = 0;

        // 分页相关
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalPages = 1;

        // 日志相关
        this.logs = this.loadLogsFromStorage();
        this.progressLogIndex = -1; // 记录进度日志的位置

        this.initializeElements();
        this.bindEvents();
        this.loadAccounts();
        this.restoreLogs();
    }

    initializeElements() {
        // 控制元素
        this.accountCountInput = document.getElementById('accountCount');
        this.concurrencyInput = document.getElementById('concurrency');
        this.modeSelect = document.getElementById('mode');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.clearLogBtn = document.getElementById('clearLogBtn');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.toggleLogBtn = document.getElementById('toggleLogBtn');

        // 分页元素
        this.prevPageBtn = document.getElementById('prevPageBtn');
        this.nextPageBtn = document.getElementById('nextPageBtn');
        this.paginationInfo = document.getElementById('paginationInfo');

        // 状态元素
        this.totalCountEl = document.getElementById('totalCount');
        this.successCountEl = document.getElementById('successCount');
        this.failedCountEl = document.getElementById('failedCount');
        this.progressPercentEl = document.getElementById('progressPercent');
        this.progressFillEl = document.getElementById('progressFill');
        this.logContainer = document.getElementById('logContainer');

        // 表格元素
        this.accountsTableBody = document.getElementById('accountsTableBody');
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startRegistration());
        this.stopBtn.addEventListener('click', () => this.stopRegistration());
        this.exportBtn.addEventListener('click', () => this.exportApiKeys());
        this.clearLogBtn.addEventListener('click', () => this.clearLogs());
        this.refreshBtn.addEventListener('click', () => this.loadAccounts());
        this.toggleLogBtn.addEventListener('click', () => this.toggleLog());

        // 分页事件
        this.prevPageBtn.addEventListener('click', () => this.goToPrevPage());
        this.nextPageBtn.addEventListener('click', () => this.goToNextPage());
    }

    async startRegistration() {
        if (this.isRegistering) return;

        const count = parseInt(this.accountCountInput.value);
        const concurrency = parseInt(this.concurrencyInput.value);
        const mode = this.modeSelect.value;

        if (count < 1 || count > 1000) {
            alert('注册数量必须在1-1000之间');
            return;
        }

        if (concurrency < 1 || concurrency > 10) {
            alert('并发数必须在1-10之间');
            return;
        }

        this.isRegistering = true;
        this.currentProgress = 0;
        this.totalTasks = count;

        // 更新按钮状态
        this.updateButtonStates();
        
        this.log(`🚀 开始${mode === 'parallel' ? '并行' : '串行'}注册 ${count} 个账户，并发数: ${concurrency}`);

        try {
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    count: count,
                    concurrency: concurrency,
                    mode: mode
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        this.handleRegistrationUpdate(data);
                    } catch (e) {
                        // 忽略非JSON行
                        if (line.trim()) {
                            this.log(line);
                        }
                    }
                }
            }

        } catch (error) {
            this.log(`❌ 注册过程出错: ${error.message}`);
        } finally {
            this.isRegistering = false;
            this.updateButtonStates();
            this.loadAccounts();

            // 清空进度
            this.resetProgress();
        }
    }

    handleRegistrationUpdate(data) {
        switch (data.type) {
            case 'progress':
                this.updateProgress(data.current, data.total);
                break;
            case 'account':
                this.addAccountToTable(data.account);
                break;
            case 'log':
                this.logOptimized(data.message);
                break;
            case 'complete':
                this.log(`✅ 注册成功！总注册成功 ${data.success}个！`);
                this.progressLogIndex = -1; // 重置进度日志索引
                break;
        }
    }

    // 优化的日志显示，只显示关键信息
    logOptimized(message) {
        // 过滤掉所有详细的注册过程消息和成功消息
        if (message.includes('🎉 注册成功') ||
            message.includes('❌ 注册失败') ||
            message.includes('🚀 开始注册账户') ||
            message.includes('📧 注册邮箱') ||
            message.includes('开始') ||
            message.includes('获取') ||
            message.includes('设置2FA') ||
            message.includes('等待') ||
            message.includes('验证')) {
            return; // 不显示这些详细过程
        }

        // 只显示开始和完成消息
        if (message.includes('注册完成') ||
            message.includes('开始并行注册') ||
            message.includes('开始串行注册')) {
            this.log(message);
        }
    }

    // 更新进度日志（在同一行更新）
    updateProgressLog(message) {
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        const logLine = `[${timestamp}] ${message}`;

        if (this.progressLogIndex === -1) {
            // 第一次添加进度日志
            this.logs.push(logLine);
            this.progressLogIndex = this.logs.length - 1;
        } else {
            // 更新现有的进度日志
            this.logs[this.progressLogIndex] = logLine;
        }

        // 重新渲染日志
        this.renderLogs();
        this.saveLogsToStorage();
    }

    updateProgress(current, total) {
        this.currentProgress = current;
        this.totalTasks = total;

        const percent = Math.round((current / total) * 100);
        this.progressPercentEl.textContent = `${percent}%`;
        this.progressFillEl.style.width = `${percent}%`;

        // 更新进度日志（在同一行更新）
        const progressMessage = `🎉 注册进度[${current}/${total}]`;
        this.updateProgressLog(progressMessage);
    }

    // 生成ASCII进度图
    generateProgressBar(current, total, length = 20) {
        const filled = Math.round((current / total) * length);
        const empty = length - filled;

        const filledChar = '█';
        const emptyChar = '░';

        return filledChar.repeat(filled) + emptyChar.repeat(empty);
    }

    addAccountToTable(account) {
        this.accounts.push(account);
        this.updateStats();
        this.renderAccountsTable();
    }

    updateStats() {
        const total = this.accounts.length;
        const success = this.accounts.filter(acc => acc.status === 'verified').length;
        const failed = total - success;

        this.totalCountEl.textContent = total;
        this.successCountEl.textContent = success;
        this.failedCountEl.textContent = failed;
    }

    renderAccountsTable() {
        this.accountsTableBody.innerHTML = '';

        // 计算分页
        this.totalPages = Math.ceil(this.accounts.length / this.pageSize);
        if (this.totalPages === 0) this.totalPages = 1;

        // 确保当前页在有效范围内
        if (this.currentPage > this.totalPages) {
            this.currentPage = this.totalPages;
        }

        // 计算当前页的数据范围
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, this.accounts.length);
        const currentPageAccounts = this.accounts.slice(startIndex, endIndex);

        // 渲染当前页的账户
        currentPageAccounts.forEach((account, index) => {
            const row = document.createElement('tr');

            const statusBadge = account.status === 'verified'
                ? '<span class="status-badge status-success">成功</span>'
                : '<span class="status-badge status-failed">失败</span>';

            const apiKey = account.apiKey
                ? `<span class="api-key">${account.apiKey.slice(-8)}</span>`
                : '<span class="api-key">N/A</span>';

            const twoFA = account.twoFactorEnabled ? '✅' : '❌';

            const balance = account.creditBalance
                ? `$${(account.creditBalance.totalCredits / ********).toFixed(2)}`
                : '$0.00';

            const registeredAt = account.registeredAt
                ? new Date(account.registeredAt).toLocaleString('zh-CN')
                : '未知';

            row.innerHTML = `
                <td>${startIndex + index + 1}</td>
                <td>${account.email}</td>
                <td>${apiKey}</td>
                <td>${twoFA}</td>
                <td>${balance}</td>
                <td>${statusBadge}</td>
                <td>${registeredAt}</td>
            `;

            this.accountsTableBody.appendChild(row);
        });

        // 更新分页信息
        this.updatePaginationInfo();
    }

    async loadAccounts() {
        try {
            const response = await fetch('/api/accounts');
            if (response.ok) {
                this.accounts = await response.json();
                this.updateStats();
                this.renderAccountsTable();
            }
        } catch (error) {
            this.log(`❌ 加载账户列表失败: ${error.message}`);
        }
    }

    async exportApiKeys() {
        const successAccounts = this.accounts.filter(acc => acc.status === 'verified' && acc.apiKey);
        
        if (successAccounts.length === 0) {
            alert('没有可导出的API Key');
            return;
        }

        const apiKeys = successAccounts.map(acc => acc.apiKey).join('\n');
        
        const blob = new Blob([apiKeys], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `targon_api_keys_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        this.log(`📥 已导出 ${successAccounts.length} 个API Key`);
    }

    // 更新按钮状态
    updateButtonStates() {
        this.startBtn.disabled = this.isRegistering;
        this.stopBtn.disabled = !this.isRegistering;
    }

    // 重置进度
    resetProgress() {
        this.currentProgress = 0;
        this.totalTasks = 0;
        this.progressPercentEl.textContent = '0%';
        this.progressFillEl.style.width = '0%';
        this.progressLogIndex = -1; // 重置进度日志索引
    }

    // 更新分页信息
    updatePaginationInfo() {
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(this.currentPage * this.pageSize, this.accounts.length);

        this.paginationInfo.textContent = `第 ${this.currentPage} 页，共 ${this.accounts.length} 条记录 (${startIndex}-${endIndex})`;

        // 更新分页按钮状态
        this.prevPageBtn.disabled = this.currentPage <= 1;
        this.nextPageBtn.disabled = this.currentPage >= this.totalPages;
    }

    // 上一页
    goToPrevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.renderAccountsTable();
        }
    }

    // 下一页
    goToNextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.renderAccountsTable();
        }
    }

    // 清空日志
    clearLogs() {
        if (!confirm('确定要清空所有日志吗？')) {
            return;
        }

        this.logs = [];
        this.progressLogIndex = -1; // 重置进度日志索引
        this.logContainer.innerHTML = '';
        this.saveLogsToStorage();
        this.log('🗑️ 日志已清空');
    }

    stopRegistration() {
        if (!this.isRegistering) return;
        
        // 这里可以发送停止请求到后端
        fetch('/api/stop', { method: 'POST' })
            .then(() => {
                this.log('⏹️ 已请求停止注册');
            })
            .catch(error => {
                this.log(`❌ 停止请求失败: ${error.message}`);
            });
    }

    toggleLog() {
        const isVisible = this.logContainer.classList.contains('show');
        if (isVisible) {
            this.logContainer.classList.remove('show');
            this.toggleLogBtn.innerHTML = '<span>📜</span> 显示日志';
        } else {
            this.logContainer.classList.add('show');
            this.toggleLogBtn.innerHTML = '<span>📜</span> 隐藏日志';
        }
    }

    log(message) {
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        const logLine = `[${timestamp}] ${message}`;

        // 添加到日志数组
        this.logs.push(logLine);

        // 限制日志数量，避免内存过大
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-1000);
            // 重新调整进度日志索引
            if (this.progressLogIndex >= 0) {
                this.progressLogIndex = Math.max(0, this.progressLogIndex - (this.logs.length - 1000));
            }
        }

        // 重新渲染日志
        this.renderLogs();

        // 保存到本地存储
        this.saveLogsToStorage();

        console.log(logLine);
    }

    // 渲染日志
    renderLogs() {
        this.logContainer.innerHTML = this.logs.join('\n') + '\n';
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
    }

    // 保存日志到本地存储
    saveLogsToStorage() {
        try {
            localStorage.setItem('targon_logs', JSON.stringify(this.logs));
        } catch (error) {
            console.error('保存日志失败:', error);
        }
    }

    // 从本地存储加载日志
    loadLogsFromStorage() {
        try {
            const saved = localStorage.getItem('targon_logs');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('加载日志失败:', error);
            return [];
        }
    }

    // 恢复日志显示
    restoreLogs() {
        if (this.logs.length > 0) {
            this.logContainer.innerHTML = this.logs.join('\n') + '\n';
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }
    }
}

// 初始化界面
document.addEventListener('DOMContentLoaded', () => {
    new TargonWebInterface();
});
