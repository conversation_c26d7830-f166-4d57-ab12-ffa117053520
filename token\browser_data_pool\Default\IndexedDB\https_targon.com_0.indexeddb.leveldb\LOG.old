2025/08/02-17:29:04.889 34d4 Reusing MANIFEST C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/MANIFEST-000001
2025/08/02-17:29:04.890 34d4 Recovering log #20
2025/08/02-17:29:04.891 34d4 Reusing old log C:\Users\<USER>\Desktop\targon_add\token\browser_data_pool\Default\IndexedDB\https_targon.com_0.indexeddb.leveldb/000020.log 
2025/08/02-17:29:04.906 1de4 Level-0 table #25: started
2025/08/02-17:29:04.915 1de4 Level-0 table #25: 321715 bytes OK
2025/08/02-17:29:04.920 1de4 Delete type=0 #20
2025/08/02-17:29:04.921 34d4 Compacting 4@0 + 0@1 files
2025/08/02-17:29:04.928 34d4 Generated table #26@0: 285 keys, 6762 bytes
2025/08/02-17:29:04.928 34d4 Compacted 4@0 + 0@1 files => 6762 bytes
2025/08/02-17:29:04.933 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:29:04.934 34d4 Delete type=2 #25
2025/08/02-17:29:04.934 1de4 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:29:04.986 1de4 Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:29:05.132 1de4 Level-0 table #28: started
2025/08/02-17:29:05.140 1de4 Level-0 table #28: 164689 bytes OK
2025/08/02-17:29:05.145 1de4 Delete type=2 #17
2025/08/02-17:29:05.145 1de4 Delete type=2 #19
2025/08/02-17:29:05.145 1de4 Delete type=2 #21
2025/08/02-17:29:05.145 1de4 Delete type=0 #24
2025/08/02-17:29:05.146 1de4 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x07\x00\x00\x05' @ 2169 : 1
2025/08/02-17:29:05.146 1de4 Compacting 1@0 + 1@1 files
2025/08/02-17:29:05.161 1de4 Generated table #29@0: 301 keys, 40133 bytes
2025/08/02-17:29:05.161 1de4 Compacted 1@0 + 1@1 files => 40133 bytes
2025/08/02-17:29:05.164 1de4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:29:05.164 34d4 Manual compaction at level-0 from '\x00\x07\x00\x00\x05' @ 2169 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:29:05.193 1de4 Level-0 table #31: started
2025/08/02-17:29:05.201 1de4 Level-0 table #31: 516 bytes OK
2025/08/02-17:29:05.204 1de4 Delete type=2 #26
2025/08/02-17:29:05.204 1de4 Delete type=0 #27
2025/08/02-17:29:05.204 1de4 Delete type=2 #28
2025/08/02-17:29:05.205 34d4 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x08\x00\x00\x05' @ 2185 : 1
2025/08/02-17:29:05.205 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:29:05.211 34d4 Generated table #32@0: 302 keys, 40161 bytes
2025/08/02-17:29:05.211 34d4 Compacted 1@0 + 1@1 files => 40161 bytes
2025/08/02-17:29:05.214 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:29:05.214 34d4 Delete type=2 #31
2025/08/02-17:29:05.214 34d4 Manual compaction at level-0 from '\x00\x08\x00\x00\x05' @ 2185 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:35.976 56c0 Level-0 table #34: started
2025/08/02-17:32:35.990 56c0 Level-0 table #34: 548778 bytes OK
2025/08/02-17:32:36.003 56c0 Delete type=2 #29
2025/08/02-17:32:36.003 56c0 Delete type=0 #30
2025/08/02-17:32:36.004 54cc Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x09\x00\x00\x05' @ 2545 : 1
2025/08/02-17:32:36.004 54cc Compacting 1@0 + 1@1 files
2025/08/02-17:32:36.011 54cc Generated table #35@0: 343 keys, 40329 bytes
2025/08/02-17:32:36.011 54cc Compacted 1@0 + 1@1 files => 40329 bytes
2025/08/02-17:32:36.014 54cc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.014 54cc Delete type=2 #34
2025/08/02-17:32:36.015 34d4 Manual compaction at level-0 from '\x00\x09\x00\x00\x05' @ 2545 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.051 1de4 Level-0 table #37: started
2025/08/02-17:32:36.113 1de4 Level-0 table #37: 501 bytes OK
2025/08/02-17:32:36.118 1de4 Delete type=2 #32
2025/08/02-17:32:36.118 1de4 Delete type=0 #33
2025/08/02-17:32:36.120 34d4 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 2561 : 1
2025/08/02-17:32:36.120 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:32:36.126 34d4 Generated table #38@0: 344 keys, 40360 bytes
2025/08/02-17:32:36.126 34d4 Compacted 1@0 + 1@1 files => 40360 bytes
2025/08/02-17:32:36.131 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.132 34d4 Delete type=2 #37
2025/08/02-17:32:36.132 34d4 Manual compaction at level-0 from '\x00\x0a\x00\x00\x05' @ 2561 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.230 56c0 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.324 56c0 Level-0 table #40: started
2025/08/02-17:32:36.335 56c0 Level-0 table #40: 364167 bytes OK
2025/08/02-17:32:36.340 56c0 Delete type=2 #35
2025/08/02-17:32:36.340 56c0 Delete type=0 #36
2025/08/02-17:32:36.341 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 2640 : 1
2025/08/02-17:32:36.341 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:32:36.350 34d4 Generated table #41@0: 360 keys, 6817 bytes
2025/08/02-17:32:36.350 34d4 Compacted 1@0 + 1@1 files => 6817 bytes
2025/08/02-17:32:36.354 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.354 34d4 Delete type=2 #40
2025/08/02-17:32:36.355 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x05' @ 2640 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.398 1de4 Level-0 table #43: started
2025/08/02-17:32:36.403 1de4 Level-0 table #43: 1802 bytes OK
2025/08/02-17:32:36.409 1de4 Delete type=2 #38
2025/08/02-17:32:36.409 1de4 Delete type=0 #39
2025/08/02-17:32:36.410 1de4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.468 1de4 Level-0 table #45: started
2025/08/02-17:32:36.481 1de4 Level-0 table #45: 1522 bytes OK
2025/08/02-17:32:36.487 1de4 Delete type=0 #42
2025/08/02-17:32:36.487 1de4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.494 56c0 Level-0 table #47: started
2025/08/02-17:32:36.505 56c0 Level-0 table #47: 640 bytes OK
2025/08/02-17:32:36.509 56c0 Delete type=0 #44
2025/08/02-17:32:36.509 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.559 1de4 Level-0 table #49: started
2025/08/02-17:32:36.570 1de4 Level-0 table #49: 1401 bytes OK
2025/08/02-17:32:36.579 1de4 Delete type=0 #46
2025/08/02-17:32:36.579 34d4 Compacting 4@0 + 1@1 files
2025/08/02-17:32:36.591 34d4 Generated table #50@0: 448 keys, 7555 bytes
2025/08/02-17:32:36.591 34d4 Compacted 4@0 + 1@1 files => 7555 bytes
2025/08/02-17:32:36.600 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.601 34d4 Delete type=2 #49
2025/08/02-17:32:36.602 1de4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.637 1de4 Level-0 table #52: started
2025/08/02-17:32:36.643 1de4 Level-0 table #52: 1638 bytes OK
2025/08/02-17:32:36.650 1de4 Delete type=2 #41
2025/08/02-17:32:36.651 1de4 Delete type=2 #43
2025/08/02-17:32:36.651 1de4 Delete type=2 #45
2025/08/02-17:32:36.651 1de4 Delete type=2 #47
2025/08/02-17:32:36.651 1de4 Delete type=0 #48
2025/08/02-17:32:36.653 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.654 1de4 Level-0 table #54: started
2025/08/02-17:32:36.664 1de4 Level-0 table #54: 787 bytes OK
2025/08/02-17:32:36.667 1de4 Delete type=0 #51
2025/08/02-17:32:36.668 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.673 34d4 Level-0 table #56: started
2025/08/02-17:32:36.681 34d4 Level-0 table #56: 670 bytes OK
2025/08/02-17:32:36.685 34d4 Delete type=0 #53
2025/08/02-17:32:36.686 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.687 34d4 Level-0 table #58: started
2025/08/02-17:32:36.694 34d4 Level-0 table #58: 229 bytes OK
2025/08/02-17:32:36.698 34d4 Delete type=0 #55
2025/08/02-17:32:36.698 1de4 Compacting 4@0 + 1@1 files
2025/08/02-17:32:36.705 1de4 Generated table #59@0: 500 keys, 8236 bytes
2025/08/02-17:32:36.705 1de4 Compacted 4@0 + 1@1 files => 8236 bytes
2025/08/02-17:32:36.711 1de4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.712 1de4 Delete type=2 #58
2025/08/02-17:32:36.713 1de4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.747 1de4 Level-0 table #61: started
2025/08/02-17:32:36.753 1de4 Level-0 table #61: 1347 bytes OK
2025/08/02-17:32:36.761 1de4 Delete type=2 #50
2025/08/02-17:32:36.761 1de4 Delete type=2 #52
2025/08/02-17:32:36.761 1de4 Delete type=2 #54
2025/08/02-17:32:36.761 1de4 Delete type=2 #56
2025/08/02-17:32:36.761 1de4 Delete type=0 #57
2025/08/02-17:32:36.762 1de4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.764 1de4 Level-0 table #63: started
2025/08/02-17:32:36.773 1de4 Level-0 table #63: 549 bytes OK
2025/08/02-17:32:36.776 1de4 Delete type=0 #60
2025/08/02-17:32:36.777 1de4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.782 1de4 Level-0 table #65: started
2025/08/02-17:32:36.788 1de4 Level-0 table #65: 32135 bytes OK
2025/08/02-17:32:36.793 1de4 Delete type=0 #62
2025/08/02-17:32:36.794 34d4 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x01\x01\x03\x03\x00\x90\xeaH\xa1\x86yB' @ 3156 : 0
2025/08/02-17:32:36.794 34d4 Compacting 3@0 + 1@1 files
2025/08/02-17:32:36.808 34d4 Generated table #66@0: 535 keys, 39774 bytes
2025/08/02-17:32:36.809 34d4 Compacted 3@0 + 1@1 files => 39774 bytes
2025/08/02-17:32:36.813 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.814 34d4 Delete type=2 #65
2025/08/02-17:32:36.815 34d4 Manual compaction at level-0 from '\x00\x01\x01\x03\x03\x00\x90\xeaH\xa1\x86yB' @ 3156 : 0 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.863 1de4 Level-0 table #68: started
2025/08/02-17:32:36.873 1de4 Level-0 table #68: 489601 bytes OK
2025/08/02-17:32:36.878 1de4 Delete type=2 #59
2025/08/02-17:32:36.878 1de4 Delete type=2 #61
2025/08/02-17:32:36.878 1de4 Delete type=2 #63
2025/08/02-17:32:36.878 1de4 Delete type=0 #64
2025/08/02-17:32:36.879 34d4 Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 3367 : 0
2025/08/02-17:32:36.880 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:32:36.885 34d4 Generated table #69@0: 569 keys, 9909 bytes
2025/08/02-17:32:36.885 34d4 Compacted 1@0 + 1@1 files => 9909 bytes
2025/08/02-17:32:36.889 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.890 34d4 Delete type=2 #68
2025/08/02-17:32:36.890 34d4 Manual compaction at level-0 from '\x00\x0c\x00\x00\x05' @ 3367 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:36.970 1de4 Level-0 table #71: started
2025/08/02-17:32:36.980 1de4 Level-0 table #71: 239676 bytes OK
2025/08/02-17:32:36.985 1de4 Delete type=2 #66
2025/08/02-17:32:36.985 1de4 Delete type=0 #67
2025/08/02-17:32:36.986 1de4 Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x05' @ 3468 : 1
2025/08/02-17:32:36.987 1de4 Compacting 1@0 + 1@1 files
2025/08/02-17:32:36.993 1de4 Generated table #72@0: 611 keys, 108984 bytes
2025/08/02-17:32:36.993 1de4 Compacted 1@0 + 1@1 files => 108984 bytes
2025/08/02-17:32:36.997 1de4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:36.998 5e64 Manual compaction at level-0 from '\x00\x0d\x00\x00\x05' @ 3468 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:37.048 34d4 Level-0 table #74: started
2025/08/02-17:32:37.056 34d4 Level-0 table #74: 520 bytes OK
2025/08/02-17:32:37.059 34d4 Delete type=2 #69
2025/08/02-17:32:37.059 34d4 Delete type=0 #70
2025/08/02-17:32:37.059 34d4 Delete type=2 #71
2025/08/02-17:32:37.060 34d4 Manual compaction at level-0 from '\x00\x0e\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0f\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x00\x00\x05' @ 3484 : 1
2025/08/02-17:32:37.060 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:32:37.066 34d4 Generated table #75@0: 615 keys, 109021 bytes
2025/08/02-17:32:37.067 34d4 Compacted 1@0 + 1@1 files => 109021 bytes
2025/08/02-17:32:37.071 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:37.071 34d4 Delete type=2 #74
2025/08/02-17:32:37.072 34d4 Manual compaction at level-0 from '\x00\x0e\x00\x00\x05' @ 3484 : 1 .. '\x00\x0f\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:37.597 1de4 Manual compaction at level-0 from '\x00\x0f\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x10\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:37.653 1de4 Level-0 table #77: started
2025/08/02-17:32:37.659 1de4 Level-0 table #77: 240212 bytes OK
2025/08/02-17:32:37.663 1de4 Delete type=2 #72
2025/08/02-17:32:37.663 1de4 Delete type=0 #73
2025/08/02-17:32:37.664 34d4 Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 3812 : 1
2025/08/02-17:32:37.664 34d4 Compacting 1@0 + 1@1 files
2025/08/02-17:32:37.673 34d4 Generated table #78@0: 710 keys, 75015 bytes
2025/08/02-17:32:37.673 34d4 Compacted 1@0 + 1@1 files => 75015 bytes
2025/08/02-17:32:37.676 34d4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/08/02-17:32:37.677 34d4 Delete type=2 #77
2025/08/02-17:32:37.677 34d4 Manual compaction at level-0 from '\x00\x10\x00\x00\x05' @ 3812 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/02-17:32:56.121 5e64 Compacting 1@1 + 1@2 files
2025/08/02-17:32:56.128 5e64 Generated table #79@1: 102 keys, 69636 bytes
2025/08/02-17:32:56.129 5e64 Compacted 1@1 + 1@2 files => 69636 bytes
2025/08/02-17:32:56.133 5e64 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/02-17:32:56.134 5e64 Delete type=2 #22
2025/08/02-17:32:56.134 5e64 Delete type=2 #75
2025/08/02-17:32:56.134 5e64 Delete type=2 #78
