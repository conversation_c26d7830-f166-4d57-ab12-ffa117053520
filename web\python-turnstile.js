const { spawn } = require('child_process');
const path = require('path');

/**
 * Python Turnstile Token 池服务类
 * 管理多标签页 Token 池，支持并发获取和分配 Token
 */
class PythonTurnstileService {
    constructor(pythonPath = 'python', maxTabs = 10) {
        this.pythonPath = pythonPath;
        this.maxTabs = maxTabs;
        this.scriptPath = path.join(__dirname, '..', 'token', 'simple_turnstile.py');
        this.poolProcess = null;
        this.isPoolRunning = false;
        this.tokenQueue = [];
    }

    /**
     * 启动 Token 池
     * @returns {Promise<boolean>} 启动是否成功
     */
    async startTokenPool() {
        if (this.isPoolRunning) {
            console.log('🐍 Token 池已在运行中');
            return true;
        }

        return new Promise((resolve) => {
            try {
                console.log(`🐍 启动 Token 池，最大标签页数: ${this.maxTabs}`);
                console.log(`🐍 Python 路径: ${this.pythonPath}`);
                console.log(`🐍 脚本路径: ${this.scriptPath}`);
                console.log(`🐍 工作目录: ${path.dirname(this.scriptPath)}`);
                console.log(`🐍 命令参数: ['${this.scriptPath}', 'start', '${this.maxTabs.toString()}']`);

                this.poolProcess = spawn(this.pythonPath, [this.scriptPath, 'start', this.maxTabs.toString()], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    cwd: path.dirname(this.scriptPath)
                });

                let hasResolved = false;

                // 设置超时
                const timeout = setTimeout(() => {
                    if (!hasResolved) {
                        hasResolved = true;
                        console.log('⏰ Token 池启动超时');
                        resolve(false);
                    }
                }, 180000); // 3分钟超时

                this.poolProcess.stdout.on('data', (data) => {
                    const output = data.toString();
                    const lines = output.split('\n').filter(line => line.trim());

                    lines.forEach(line => {
                        if (line.startsWith('{')) {
                            try {
                                const jsonData = JSON.parse(line);
                                if (jsonData.success && !hasResolved) {
                                    hasResolved = true;
                                    clearTimeout(timeout);
                                    this.isPoolRunning = true;
                                    console.log(`✅ Token 池启动成功，可用 Token 数: ${jsonData.available_tokens}`);
                                    resolve(true);
                                } else if (!jsonData.success && !hasResolved) {
                                    hasResolved = true;
                                    clearTimeout(timeout);
                                    console.log(`❌ Token 池启动失败: ${jsonData.message}`);
                                    resolve(false);
                                }
                            } catch (e) {
                                // 不是JSON，可能是日志
                                console.log(`🐍 ${line}`);
                            }
                        } else {
                            console.log(`🐍 ${line}`);
                        }
                    });
                });

                this.poolProcess.stderr.on('data', (data) => {
                    const errorMsg = data.toString().trim();
                    console.error(`🐍 Python 错误输出: ${errorMsg}`);
                });

                this.poolProcess.on('close', (code) => {
                    clearTimeout(timeout);
                    this.isPoolRunning = false;

                    if (!hasResolved) {
                        hasResolved = true;
                        console.log(`❌ Token 池进程退出，退出码: ${code}`);
                        resolve(false);
                    }
                });

                this.poolProcess.on('error', (error) => {
                    clearTimeout(timeout);
                    if (!hasResolved) {
                        hasResolved = true;
                        console.error('❌ Token 池进程启动失败:', error.message);
                        console.error('❌ 错误代码:', error.code);
                        console.error('❌ 错误详情:', error);
                        resolve(false);
                    }
                });

            } catch (error) {
                console.error('❌ 启动 Token 池失败:', error.message);
                resolve(false);
            }
        });
    }

    /**
     * 获取一个可用的 Token
     * @returns {Promise<string|null>} 返回 token 或 null
     */
    async getTurnstileToken() {
        // 如果池未运行，先启动
        if (!this.isPoolRunning) {
            const started = await this.startTokenPool();
            if (!started) {
                return null;
            }
            // 等待池完全启动
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // 从本地队列获取（如果有的话）
        if (this.tokenQueue.length > 0) {
            const token = this.tokenQueue.shift();
            console.log('✅ 从本地队列获取 Token');
            return token;
        }

        // 如果本地队列为空，这里可以实现与 Python 池的通信
        // 暂时返回 null，让调用者重试
        console.log('⚠️ 本地 Token 队列为空，需要实现池通信机制');
        return null;
    }

    /**
     * 停止 Token 池
     * @returns {Promise<boolean>} 停止是否成功
     */
    async stopTokenPool() {
        if (!this.isPoolRunning || !this.poolProcess) {
            console.log('🐍 Token 池未运行');
            return true;
        }

        return new Promise((resolve) => {
            try {
                console.log('🐍 正在停止 Token 池...');

                // 发送终止信号
                this.poolProcess.kill('SIGTERM');

                // 设置超时
                const timeout = setTimeout(() => {
                    console.log('⏰ 强制终止 Token 池进程');
                    this.poolProcess.kill('SIGKILL');
                    this.isPoolRunning = false;
                    resolve(true);
                }, 10000); // 10秒超时

                this.poolProcess.on('close', () => {
                    clearTimeout(timeout);
                    this.isPoolRunning = false;
                    this.poolProcess = null;
                    console.log('✅ Token 池已停止');
                    resolve(true);
                });

            } catch (error) {
                console.error('❌ 停止 Token 池失败:', error.message);
                this.isPoolRunning = false;
                resolve(false);
            }
        });
    }

    /**
     * 预填充 Token 队列（用于测试）
     * @param {Array<string>} tokens Token 数组
     */
    preloadTokens(tokens) {
        this.tokenQueue = [...tokens];
        console.log(`🐍 预加载 ${tokens.length} 个 Token 到本地队列`);
    }

    /**
     * 获取服务状态
     * @returns {Object} 服务状态信息
     */
    getStatus() {
        return {
            isPoolRunning: this.isPoolRunning,
            maxTabs: this.maxTabs,
            localTokenCount: this.tokenQueue.length,
            hasPoolProcess: this.poolProcess !== null
        };
    }

    /**
     * 检查 Python 环境是否可用
     * @returns {Promise<boolean>}
     */
    async checkPythonEnvironment() {
        return new Promise((resolve) => {
            try {
                const pythonProcess = spawn(this.pythonPath, ['--version'], {
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                pythonProcess.on('close', (code) => {
                    resolve(code === 0);
                });

                pythonProcess.on('error', () => {
                    resolve(false);
                });

            } catch (error) {
                resolve(false);
            }
        });
    }
}

module.exports = PythonTurnstileService;
